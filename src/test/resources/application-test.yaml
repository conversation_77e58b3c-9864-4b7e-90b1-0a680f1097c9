# Test properties for ProcessTokenTransfersCommandHandlerTest
onre:
  token:
    one:
      address: 5Y8NV33Vv7WbnLfq3zBcKSdYPrk7g2KoiQoe7M2tcxp5
      token-block-tge: 331308318
      token-tge-timestamp: "2022-12-31T00:00:00Z"  # Before test dates, TGE doesn't matter with mocked clock

spring:
  # Zonky embedded database will automatically configure the datasource
  # No explicit datasource configuration needed - it will use an in-memory PostgreSQL instance
  test:
    database:
      replace: none  # Don't replace the datasource, use the embedded one

quartz:
  enabled: false
  jobs: [ ]